﻿using Microsoft.EntityFrameworkCore;
using GoTRUST.EMR.Domain.Models;

namespace GoTRUST.EMR.Application.Data;

public interface IApplicationDbContext
{
    DbSet<ForgotPasswordSession> ForgotPasswordSessions { get; }
    DbSet<RefreshToken> RefreshTokens { get; }
    DbSet<RefreshTokenHistory> RefreshTokenHistories { get; }
    DbSet<NotificationTemplate> NotificationTemplates { get; }
    DbSet<Function> Functions { get; }
    DbSet<RoleFunction> RoleFunctions { get; }
    DbSet<SystemConfig> SystemConfigs { get; }
    DbSet<HoatChat> HoatChats { get; }
    DbSet<PhuongPhapCheBien> PhuongPhapCheBiens { get; }
    DbSet<NhomChiPhi> NhomChiPhis { get; }
    DbSet<Khoa> Khoas { get; }
    DbSet<DichVuKyThuat> DichVuKyThuats { get; }
    DbSet<NgheNghiep> <PERSON>heN<PERSON>ie<PERSON> { get; }
    DbSet<Benh> Benhs { get; }
    DbSet<Tinh> Tinhs { get; }
    DbSet<Huyen> Huyens { get; }
    DbSet<Xa> Xas { get; }
    DbSet<DanToc> DanTocs { get; }
    DbSet<QuocTich> QuocTiches { get; }
    DbSet<DoiTuongKCB> DoiTuongKCBs { get; }
    DbSet<PhauThuatThuThuat> PhauThuatThuThuats { get; }
    DbSet<TaiNan> TaiNans { get; }
    DbSet<VatTuYTe> VatTuYTes { get; }
    DbSet<XangDau> XangDaus { get; }
    DbSet<XetNghiem> XetNghiems { get; }
    DbSet<TonGiao> TonGiaos { get; }
    DbSet<TinhTrangHonNhan> TinhTrangHonNhans { get; }
    DbSet<PhacDoDieuTriHIVvaAIDS> PhacDoDieuTriHIVvaAIDSs { get; }
    DbSet<LoaiKCB> LoaiKCBs { get; }

    // EMR Medical Records System
    DbSet<Hospital> Hospitals { get; }
    DbSet<ApiKey> ApiKeys { get; }
    DbSet<DigitalSignature> DigitalSignatures { get; }
    DbSet<Department> Departments { get; }
    DbSet<Employee> Employees { get; }
    DbSet<Patient> Patients { get; }
    DbSet<PatientHealthMetric> PatientHealthMetrics { get; }
    DbSet<PatientMedicalHistory> PatientMedicalHistories { get; }
    DbSet<PatientShareInfo> PatientShareInfos { get; }
    DbSet<MedicalRecord> MedicalRecords { get; }
    DbSet<FilmDetail> FilmDetails { get; }
    DbSet<MedicalRecordFile> MedicalRecordFiles { get; }
    DbSet<MedicalRecordTemplate> MedicalRecordTemplates { get; }
    DbSet<MedicalRecordInstanceData> MedicalRecordInstanceDatas { get; }
    DbSet<MedicalRecordStatusHistory> MedicalRecordStatusHistories { get; }
    DbSet<BorrowRequest> BorrowRequests { get; }
    DbSet<BorrowerOrganization> BorrowerOrganizations { get; }
    DbSet<AdjustmentRequest> AdjustmentRequests { get; }
    DbSet<UserActionLog> UserActionLogs { get; }
    DbSet<SystemLog> SystemLogs { get; }
    DbSet<TwoFactorCode> TwoFactorCodes { get; }
    DbSet<HospitalConfig> HospitalConfigs { get; }
    DbSet<Notification> Notifications { get; }
    DbSet<NotificationRecipient> NotificationRecipients { get; }
    DbSet<FileSystemNode> FileSystemNodes { get; }
    DbSet<UserFileSystemNode> UserFileSystemNodes { get; }
    DbSet<RoleFileSystemNode> RoleFileSystemNodes { get; }
    DbSet<TransactionSign> TransactionSigns { get; }
    Task<int> SaveChangesAsync(CancellationToken cancellationToken);
}
