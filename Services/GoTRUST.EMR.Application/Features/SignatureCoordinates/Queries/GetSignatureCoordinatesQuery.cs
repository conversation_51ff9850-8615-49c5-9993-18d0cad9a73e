using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.SignatureCoordinates.Queries;

public record GetSignatureCoordinatesQuery(Guid MedicalRecordTemplateId)
    : IQuery<Response<List<SignatureCoordinateDto>>>;

public record SignatureCoordinateDto(
    Guid Id,
    Guid MedicalRecordTemplateId,
    Guid RoleId,
    string Name,
    decimal CoordinateX,
    decimal CoordinateY,
    int PageNumber,
    decimal Width,
    decimal Height,
    bool AllowAutoSign,
    string? RoleName,
    string? TemplateName
);

public class GetSignatureCoordinatesQueryHandler(
    IApplicationDbContext context,
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager,
    RoleManager<Role> roleManager,
    ILogger<GetSignatureCoordinatesQueryHandler> logger)
    : I<PERSON><PERSON>yHandler<GetSignatureCoordinatesQuery, Response<List<SignatureCoordinateDto>>>
{
    public async Task<Response<List<SignatureCoordinateDto>>> Handle(
        GetSignatureCoordinatesQuery request,
        CancellationToken cancellationToken)
    {
        // Get current user ID from authentication context
        var userId = httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");

        logger.LogInformation("Getting signature coordinates for template {TemplateId} and user {UserId}",
            request.MedicalRecordTemplateId, userId);

        // Get user and their roles
        var user = await userManager.FindByIdAsync(userId.ToString())
            ?? throw new UserNotFoundException();

        var userRoles = await userManager.GetRolesAsync(user);
        if (!userRoles.Any())
        {
            logger.LogWarning("User {UserId} has no roles assigned", userId);
            return new Response<List<SignatureCoordinateDto>>(new List<SignatureCoordinateDto>());
        }

        // Get role IDs for the user
        var roleIds = new List<Guid>();
        foreach (var roleName in userRoles)
        {
            var role = await roleManager.FindByNameAsync(roleName);
            if (role != null)
            {
                roleIds.Add(role.Id);
            }
        }

        if (roleIds.Count == 0)
        {
            logger.LogWarning("No valid role IDs found for user {UserId}", userId);
            return new Response<List<SignatureCoordinateDto>>([]);
        }

        // Query signature coordinates
        var signatureCoordinates = await context.SignatureCoordinates
            .AsNoTracking()
            .Include(sc => sc.MedicalRecordTemplate)
            .Include(sc => sc.Role)
            .Where(sc => sc.MedicalRecordTemplateId == request.MedicalRecordTemplateId
                        && roleIds.Contains(sc.RoleId))
            .OrderBy(sc => sc.PageNumber)
            .ThenBy(sc => sc.CoordinateY)
            .ThenBy(sc => sc.CoordinateX)
            .ProjectToType<SignatureCoordinateDto>()
            // .Select(sc => new SignatureCoordinateDto(
            //     sc.Id,
            //     sc.MedicalRecordTemplateId,
            //     sc.RoleId,
            //     sc.Name,
            //     sc.CoordinateX,
            //     sc.CoordinateY,
            //     sc.PageNumber,
            //     sc.Width,
            //     sc.Height,
            //     sc.AllowAutoSign,
            //     sc.Role.Name,
            //     sc.MedicalRecordTemplate.TemplateName
            // ))
            .ToListAsync(cancellationToken);

        logger.LogInformation("Found {Count} signature coordinates for template {TemplateId} and user {UserId}",
            signatureCoordinates.Count, request.MedicalRecordTemplateId, userId);

        return new Response<List<SignatureCoordinateDto>>(signatureCoordinates);
    }
}
