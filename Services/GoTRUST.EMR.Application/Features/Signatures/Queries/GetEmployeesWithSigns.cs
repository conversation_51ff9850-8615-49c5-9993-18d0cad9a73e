﻿using GoTRUST.EMR.Application.Helpers;
using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.Signatures.Queries;

public record GetEmployeesWithSignsQuery : PaginationRequest, IQuery<PaginationResponse<GetEmployeesWithSignsResponse>>
{
    public string? SearchTerm { get; set; }
}

public record GetEmployeesWithSignsResponse(
    Guid Id,
    string EmployeeCode,
    string FullName,
    string Email,
    string AvatarUrl,
    string SignUsername,
    List<string> SignSerials
);

public class GetEmployeesWithSignsQueryHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager
) : IQueryHandler<GetEmployeesWithSignsQuery, PaginationResponse<GetEmployeesWithSignsResponse>>
{
    public async Task<PaginationResponse<GetEmployeesWithSignsResponse>> Handle(GetEmployeesWithSignsQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString() ?? string.Empty) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var query = dbContext.Employees
            .Include(e => e.User)
            .Where(e => e.HospitalId == hospitalId);
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var term = request.SearchTerm.Trim();
            query = query.Where(e =>
                (!string.IsNullOrEmpty(e.EmployeeCode) && e.EmployeeCode.ToLower().Contains(term.ToLower())) ||
                (!string.IsNullOrEmpty(e.FullName) && e.FullName.ToLower().Contains(term.ToLower())) ||
                (!string.IsNullOrEmpty(e.Email) && e.Email.ToLower().Contains(term.ToLower()))
            );
        }

        var employees = await query
            .AsNoTracking()
            .Include(e => e.User)
            .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize!.Value)
            .ToListAsync(cancellationToken);
        var employeeCount = await query.CountAsync(cancellationToken);

        var employeeList = new List<GetEmployeesWithSignsResponse>();

        foreach (var employee in employees)
        {
            var avatarUrl = string.Empty;
            var signUsername = string.Empty;
            List<string> signSerials = new();
            List<GetUserSignaturesResponse> signatures = new();

            if (employee.User != null)
            {
                avatarUrl = employee.User.AvatarUrl;
                signUsername = employee.User.SignUsername;
                signSerials = employee.User.SignSerials ?? new List<string>();
            }

            employeeList.Add(new GetEmployeesWithSignsResponse(
                employee.Id,
                employee.EmployeeCode,
                employee.FullName,
                employee.Email,
                avatarUrl!,
                signUsername!,
                signSerials
            ));
        }

        return new PaginationResponse<GetEmployeesWithSignsResponse>(request.PageIndex, request.PageSize, employeeCount, employeeList);
    }
}