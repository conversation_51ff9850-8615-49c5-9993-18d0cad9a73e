using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Application.Helpers;

namespace GoTRUST.EMR.Application.Features.Signatures.Queries;

public record GetUserSignSerialsQuery() : IQuery<Response<List<string>>>;

public class GetUserSignSerialsQueryHandler(
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager
) : IQueryHandler<GetUserSignSerialsQuery, Response<List<string>>>
{
    public async Task<Response<List<string>>> Handle(GetUserSignSerialsQuery request, CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await userManager.FindByIdAsync(userId.ToString() ?? string.Empty) ?? throw new UserNotFoundException();

        var signSerials = user.SignSerials ?? [];

        return new Response<List<string>>(signSerials);
    }
}
