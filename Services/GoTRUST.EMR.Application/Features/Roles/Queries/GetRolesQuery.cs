using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Roles.Queries;

public record GetRolesQuery() : IQuery<Response<List<GetRolesResponse>>>;

public record GetRolesResponse(
    Guid Id,
    string Name,
    string NormalizedName,
    string? Description
);

public class GetRolesQueryHandler(
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager
) : IQueryHandler<GetRolesQuery, Response<List<GetRolesResponse>>>
{
    public async Task<Response<List<GetRolesResponse>>> Handle(GetRolesQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var roles = _roleManager.Roles
            .Where(r => r.HospitalId == hospitalId)
            .ProjectToType<GetRolesResponse>();

        var result = await roles.ToListAsync(cancellationToken);

        return new Response<List<GetRolesResponse>>(result);
    }
}