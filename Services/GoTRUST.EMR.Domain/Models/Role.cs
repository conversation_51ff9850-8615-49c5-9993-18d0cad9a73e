using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Domain.Models;

public class Role : IdentityRole<Guid>, IAggregate
{
    [StringLength(255)]
    public string? Description { get; set; }
    public string? CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public Guid? HospitalId { get; set; }
    public virtual ICollection<RoleFileSystemNode>? RoleNodes { get; set; } = [];
    public virtual ICollection<RoleFunction>? RoleFunctions { get; set; }
    public virtual ICollection<SignatureCoordinate>? SignatureCoordinates { get; set; } = [];
    public virtual Hospital? Hospital { get; set; }
    private readonly List<IDomainEvent> _domainEvents = [];
    public IReadOnlyList<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    public void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public IDomainEvent[] ClearDomainEvents()
    {
        IDomainEvent[] dequeuedEvents = [.. _domainEvents];

        _domainEvents.Clear();

        return dequeuedEvents;
    }
}
