namespace GoTRUST.EMR.Domain.Models;

public class RoleFunction
{
    public Guid RoleId { get; set; }

    public Guid FunctionId { get; set; }

    public DateTime CreatedAt { get; set; }

    // Navigation properties
    [ForeignKey(nameof(RoleId))]
    public virtual Role Role { get; set; } = null!;

    [ForeignKey(nameof(FunctionId))]
    public virtual Function Function { get; set; } = null!;
    public string? PermissionType { get; set; } = null!;
}
