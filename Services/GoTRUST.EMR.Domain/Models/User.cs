using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Domain.Models;

public class User : IdentityUser<Guid>, IAggregate
{
    public string? FullName { get; set; }

    public bool IsActive { get; set; } = true; // Default: true
    public string? AvatarUrl { get; set; }

    public DateTime? LastLoginAt { get; set; }

    public string? CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public DateTime? LastPasswordChangeAt { get; set; }
    public Guid? HospitalId { get; set; }
    public string? UserType { get; set; }
    public string? SignUsername { get; set; }
    public List<string>? SignSerials { get; set; } = [];

    // Navigation properties
    public virtual Hospital? Hospital { get; set; }
    public virtual ICollection<SystemConfig>? SystemConfigs { get; set; }
    public virtual ICollection<NotificationRecipient> NotificationRecipients { get; set; } = [];
    public ICollection<RefreshToken> RefreshTokens { get; set; } = [];
    public ICollection<RefreshTokenHistory> RefreshTokenHistories { get; set; } = [];
    public ICollection<UserActionLog> UserActionLogs { get; set; } = [];
    public ICollection<MedicalRecordStatusHistory> MedicalRecordStatusHistories { get; set; } = [];
    public ICollection<FileSystemNode> Nodes { get; set; } = [];
    public ICollection<UserFileSystemNode> UserNodes { get; set; } = [];
    private readonly List<IDomainEvent> _domainEvents = [];
    public IReadOnlyList<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    public void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public IDomainEvent[] ClearDomainEvents()
    {
        IDomainEvent[] dequeuedEvents = [.. _domainEvents];

        _domainEvents.Clear();

        return dequeuedEvents;
    }
}
