using System.ComponentModel.DataAnnotations.Schema;
using GoTRUST.EMR.Domain.Abstractions;

namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Junction table for managing signature coordinates between MedicalRecordTemplate and Role
    /// </summary>
    public class SignatureCoordinate : Entity<Guid>
    {
        /// <summary>
        /// Medical record template ID
        /// </summary>
        public Guid MedicalRecordTemplateId { get; set; }

        /// <summary>
        /// Role ID
        /// </summary>
        public Guid RoleId { get; set; }

        /// <summary>
        /// X coordinate for signature position
        /// </summary>
        public decimal CoordinateX { get; set; }

        /// <summary>
        /// Y coordinate for signature position
        /// </summary>
        public decimal CoordinateY { get; set; }

        /// <summary>
        /// Allow automatic signing for this role on this template
        /// </summary>
        public bool AllowAutoSign { get; set; } = false;

        /// <summary>
        /// Navigation property to MedicalRecordTemplate
        /// </summary>
        [ForeignKey(nameof(MedicalRecordTemplateId))]
        public virtual MedicalRecordTemplate MedicalRecordTemplate { get; set; } = null!;

        /// <summary>
        /// Navigation property to Role
        /// </summary>
        [ForeignKey(nameof(RoleId))]
        public virtual Role Role { get; set; } = null!;
    }
}
