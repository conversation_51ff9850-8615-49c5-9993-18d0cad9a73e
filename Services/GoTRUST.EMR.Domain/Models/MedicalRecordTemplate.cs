using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Medical record template
    /// </summary>
    public class MedicalRecordTemplate : Aggregate<Guid>
    {
        /// <summary>
        /// Hospital ID
        /// </summary>
        public Guid HospitalId { get; set; }

        /// <summary>
        /// Template name
        /// </summary>
        public string TemplateName { get; set; } = string.Empty;

        /// <summary>
        /// Department ID that uses this template
        /// </summary>
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Template type ("Mẫu giấy", "Mẫu bệnh án", etc.)
        /// </summary>
        public MedicalRecordTemplateType TemplateType { get; set; } = MedicalRecordTemplateType.MedicalRecord;

        /// <summary>
        /// Template content as JSON (JSON schema for data or file info)
        /// </summary>
        public string TemplateContentJson { get; set; } = string.Empty;

        /// <summary>
        /// Template Url
        /// </summary>
        public string TemplateContent { get; set; } = string.Empty;

        /// <summary>
        /// Template code (01/BV2, 02/BV2, etc.)
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Admission type (Inpatient/Outpatient) - Loại tiếp nhận (Nội trú/Ngoại trú)
        /// </summary>
        public AdmissionType AdmissionType { get; set; } = AdmissionType.Outpatient;

        /// <summary>
        /// Navigation property to the Hospital
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;

        /// <summary>
        /// Navigation property to the Department
        /// </summary>
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Medical record data instances using this template
        /// </summary>
        public virtual ICollection<MedicalRecordInstanceData> InstanceData { get; set; } = [];
    }
}
