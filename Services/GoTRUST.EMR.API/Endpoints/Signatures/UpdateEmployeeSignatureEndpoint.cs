using GoTRUST.EMR.Application.Features.Signatures.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Signatures;

public class UpdateEmployeeSignatureRequestBody
{
    public string SignUsername { get; init; } = string.Empty;
    public List<string>? SignSerials { get; init; }
}
public class UpdateEmployeeSignatureEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/signatures/employees/{employeeId:guid}", async (Guid employeeId, [FromBody] UpdateEmployeeSignatureRequestBody body, ISender sender) =>
        {
            var request = new UpdateEmployeeSignatureRequest
            {
                EmployeeId = employeeId,
                SignUsername = body.SignUsername,
                SignSerials = body.SignSerials
            };
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DSU })
        .WithTags("Signatures")
        .WithName("UpdateEmployeeSignature")
        .Produces<Response<UpdateEmployeeSignatureResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Update employee signature")
        .WithDescription("Updates the signature information for an employee.");
    }
}