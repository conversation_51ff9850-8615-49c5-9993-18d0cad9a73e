using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace GoTRUST.EMR.Infrastructure.Data
{
    public class TokenService(IConfiguration config, UserManager<User> userManager, IApplicationDbContext dbContext) : ITokenService
    {
        private readonly IConfiguration _config = config;
        private readonly SymmetricSecurityKey _key = new SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes(config["JwtKey"]!));
        private readonly UserManager<User> _userManager = userManager;
        private readonly IApplicationDbContext _dbContext = dbContext;

        public async Task<string> CreateToken(User user, int? expireInMinutes = null)
        {
            var claims = new List<Claim>
            {
                new(ClaimConstants.HospitalId, user.HospitalId.ToString()!),
                new(JwtRegisteredClaimNames.UniqueName, user.UserName!),
                new(JwtRegisteredClaimNames.Email, user.Email ?? ""),
                new(JwtRegisteredClaimNames.GivenName, user.FullName ?? ""),
                new(ClaimTypes.NameIdentifier, user.Id.ToString())
            };

            var roles = await _userManager.GetRolesAsync(user);
            var permissions = await _dbContext.RoleFunctions
                .Include(x => x.Function)
                .Include(x => x.Role)
                .Where(x => roles.Contains(x.Role.Name) && x.Function != null && x.PermissionType != null)
                .Select(x => x.Function.Code + x.PermissionType)
                .ToListAsync();

            claims.AddRange(permissions.Distinct().Select(role => new Claim(ClaimTypes.Role, role)));

            claims.AddRange(roles.Select(role => new Claim(ClaimTypes.Role, role)));
            var creds = new SigningCredentials(_key, SecurityAlgorithms.HmacSha256Signature);

            int tokenExpireInMinutes;
            if (expireInMinutes.HasValue)
            {
                tokenExpireInMinutes = expireInMinutes.Value;
            }
            else if (!int.TryParse(_config["JwtTokenExpireInMinutes"], out tokenExpireInMinutes))
            {
                tokenExpireInMinutes = 5;
            }

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.Now.AddMinutes(tokenExpireInMinutes),
                SigningCredentials = creds,
                Issuer = _config["JwtIssuer"],
                Audience = _config["JwtAudience"]
            };

            var tokenHandler = new JwtSecurityTokenHandler();

            var token = tokenHandler.CreateToken(tokenDescriptor);

            return tokenHandler.WriteToken(token);
        }

        public string CreateRefreshToken()
        {
            var randomNumber = new byte[32];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomNumber);
            return Convert.ToBase64String(randomNumber);
        }
    }
}