using GoTRUST.EMR.Application.Data;
using GoTRUST.EMR.Domain.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Reflection;

namespace GoTRUST.EMR.Infrastructure.Data;

public class ApplicationDbContext : IdentityDbContext<User, Role, Guid>, IApplicationDbContext
{
    public ApplicationDbContext() { }

    public ApplicationDbContext(DbContextOptions options) : base(options)
    { }

    public DbSet<ForgotPasswordSession> ForgotPasswordSessions => Set<ForgotPasswordSession>();
    public DbSet<RefreshToken> RefreshTokens => Set<RefreshToken>();
    public DbSet<RefreshTokenHistory> RefreshTokenHistories => Set<RefreshTokenHistory>();
    public DbSet<NotificationTemplate> NotificationTemplates => Set<NotificationTemplate>();
    public DbSet<Function> Functions => Set<Function>();
    public DbSet<RoleFunction> RoleFunctions => Set<RoleFunction>();
    public DbSet<SystemConfig> SystemConfigs => Set<SystemConfig>();
    public DbSet<HoatChat> HoatChats => Set<HoatChat>();
    public DbSet<PhuongPhapCheBien> PhuongPhapCheBiens => Set<PhuongPhapCheBien>();
    public DbSet<NhomChiPhi> NhomChiPhis => Set<NhomChiPhi>();
    public DbSet<Khoa> Khoas => Set<Khoa>();
    public DbSet<DichVuKyThuat> DichVuKyThuats => Set<DichVuKyThuat>();
    public DbSet<NgheNghiep> NgheNghieps => Set<NgheNghiep>();
    public DbSet<Benh> Benhs => Set<Benh>();
    public DbSet<Tinh> Tinhs => Set<Tinh>();
    public DbSet<Huyen> Huyens => Set<Huyen>();
    public DbSet<Xa> Xas => Set<Xa>();
    public DbSet<DanToc> DanTocs => Set<DanToc>();
    public DbSet<QuocTich> QuocTiches => Set<QuocTich>();
    public DbSet<DoiTuongKCB> DoiTuongKCBs => Set<DoiTuongKCB>();
    public DbSet<PhauThuatThuThuat> PhauThuatThuThuats => Set<PhauThuatThuThuat>();
    public DbSet<TaiNan> TaiNans => Set<TaiNan>();
    public DbSet<VatTuYTe> VatTuYTes => Set<VatTuYTe>();
    public DbSet<XangDau> XangDaus => Set<XangDau>();
    public DbSet<XetNghiem> XetNghiems => Set<XetNghiem>();
    public DbSet<TonGiao> TonGiaos => Set<TonGiao>();
    public DbSet<TinhTrangHonNhan> TinhTrangHonNhans => Set<TinhTrangHonNhan>();
    public DbSet<PhacDoDieuTriHIVvaAIDS> PhacDoDieuTriHIVvaAIDSs => Set<PhacDoDieuTriHIVvaAIDS>();
    public DbSet<LoaiKCB> LoaiKCBs => Set<LoaiKCB>();

    // EMR Medical Records System
    public DbSet<Hospital> Hospitals => Set<Hospital>();
    public DbSet<ApiKey> ApiKeys => Set<ApiKey>();
    public DbSet<DigitalSignature> DigitalSignatures => Set<DigitalSignature>();
    public DbSet<Department> Departments => Set<Department>();
    public DbSet<Employee> Employees => Set<Employee>();
    public DbSet<Patient> Patients => Set<Patient>();
    public DbSet<PatientHealthMetric> PatientHealthMetrics => Set<PatientHealthMetric>();
    public DbSet<PatientMedicalHistory> PatientMedicalHistories => Set<PatientMedicalHistory>();
    public DbSet<PatientShareInfo> PatientShareInfos => Set<PatientShareInfo>();
    public DbSet<MedicalRecord> MedicalRecords => Set<MedicalRecord>();
    public DbSet<FilmDetail> FilmDetails => Set<FilmDetail>();
    public DbSet<MedicalRecordFile> MedicalRecordFiles => Set<MedicalRecordFile>();
    public DbSet<MedicalRecordTemplate> MedicalRecordTemplates => Set<MedicalRecordTemplate>();
    public DbSet<MedicalRecordInstanceData> MedicalRecordInstanceDatas => Set<MedicalRecordInstanceData>();
    public DbSet<MedicalRecordStatusHistory> MedicalRecordStatusHistories => Set<MedicalRecordStatusHistory>();
    public DbSet<BorrowRequest> BorrowRequests => Set<BorrowRequest>();
    public DbSet<BorrowerOrganization> BorrowerOrganizations => Set<BorrowerOrganization>();
    public DbSet<AdjustmentRequest> AdjustmentRequests => Set<AdjustmentRequest>();
    public DbSet<UserActionLog> UserActionLogs => Set<UserActionLog>();
    public DbSet<SystemLog> SystemLogs => Set<SystemLog>();
    public DbSet<TwoFactorCode> TwoFactorCodes => Set<TwoFactorCode>();
    public DbSet<HospitalConfig> HospitalConfigs => Set<HospitalConfig>();
    public DbSet<Notification> Notifications => Set<Notification>();
    public DbSet<NotificationRecipient> NotificationRecipients => Set<NotificationRecipient>();
    public DbSet<FileSystemNode> FileSystemNodes => Set<FileSystemNode>();
    public DbSet<UserFileSystemNode> UserFileSystemNodes => Set<UserFileSystemNode>();
    public DbSet<RoleFileSystemNode> RoleFileSystemNodes => Set<RoleFileSystemNode>();
    public DbSet<SignatureCoordinate> SignatureCoordinates => Set<SignatureCoordinate>();
    public DbSet<TransactionSign> TransactionSigns => Set<TransactionSign>();

    protected override void OnModelCreating(ModelBuilder builder)
    {
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(builder);

        foreach (var entityType in builder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                {
                    property.SetValueConverter(new DateTimeToUtcConverter());
                }
            }
        }
    }

    public class DateTimeToUtcConverter : ValueConverter<DateTime, DateTime>
    {
        public DateTimeToUtcConverter() : base(
            v => v.ToUniversalTime(),
            v => DateTime.SpecifyKind(v, DateTimeKind.Utc))
        {
        }
    }
}
