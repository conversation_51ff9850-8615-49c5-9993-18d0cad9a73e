using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations;

public class RoleFunctionConfiguration : IEntityTypeConfiguration<RoleFunction>
{
    public void Configure(EntityTypeBuilder<RoleFunction> builder)
    {

        builder.HasKey(rf => new { rf.RoleId, rf.FunctionId, rf.PermissionType });

        builder.HasOne(rf => rf.Role)
            .WithMany(r => r.RoleFunctions)
            .HasForeignKey(rf => rf.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(rf => rf.Function)
            .WithMany(f => f.RoleFunctions)
            .HasForeignKey(rf => rf.FunctionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
    }
}
