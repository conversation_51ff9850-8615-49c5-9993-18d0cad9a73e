using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class SignatureCoordinateConfiguration : IEntityTypeConfiguration<SignatureCoordinate>
    {
        public void Configure(EntityTypeBuilder<SignatureCoordinate> builder)
        {
            builder.HasKey(c => c.Id);
            builder.HasIndex(c => c.Id)
                .IsUnique();

            // Property configurations
            builder.Property(sc => sc.Id)
                .ValueGeneratedOnAdd();

            builder.Property(sc => sc.CoordinateX)
                .IsRequired()
                .HasPrecision(18, 6); // Precision for decimal coordinates

            builder.Property(sc => sc.CoordinateY)
                .IsRequired()
                .HasPrecision(18, 6); // Precision for decimal coordinates

            builder.Property(sc => sc.AllowAutoSign)
                .IsRequired()
                .HasDefaultValue(false);

            // Relationships
            builder.HasOne(sc => sc.MedicalRecordTemplate)
                .WithMany(mrt => mrt.SignatureCoordinates)
                .HasForeignKey(sc => sc.MedicalRecordTemplateId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(sc => sc.Role)
                .WithMany(r => r.SignatureCoordinates)
                .HasForeignKey(sc => sc.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            // Filter deleted records
            builder.HasQueryFilter(sc => !sc.IsDeleted);

            // Default values for audit fields
            builder.Property(sc => sc.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(sc => sc.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
