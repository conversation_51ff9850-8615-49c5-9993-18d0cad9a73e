using BuildingBlocks.Common.PushNotification.Models;
using Refit;

namespace BuildingBlocks.Common.PushNotification.Interfaces;

/// <summary>
/// Interface API cho Push Notification Service
/// Hỗ trợ gửi cloud message đến mobile app
/// </summary>
public interface IPushNotificationApi
{
    /// <summary>
    /// API gửi cloud message đến mobile app
    /// </summary>
    /// <param name="request">Thông tin cloud message</param>
    /// <returns>HTTP Response</returns>
    [Post("/send-cloud-message")]
    Task<HttpResponseMessage> SendCloudMessageAsync([Body] SendCloudMessageRequest request);
}
